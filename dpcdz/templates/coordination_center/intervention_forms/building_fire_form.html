<!-- نموذج حرائق البنايات والمؤسسات المتخصص -->
<div id="building-fire-form" class="intervention-form-card" style="display: none;">
    <form id="buildingFireForm" class="intervention-form">
        <div class="form-section">
        <h3 class="section-title">
            <i class="fas fa-fire"></i>
            تفاصيل حريق البنايات والمؤسسات
        </h3>
        
        <!-- مرحلة التعرف -->
        <div class="stage-section" id="building-fire-reconnaissance-stage">
            <h4 class="stage-title">
                <i class="fas fa-search"></i>
                مرحلة التعرف الميداني
            </h4>
            
            <!-- طبيعة الحريق -->
            <div class="form-group">
                <label for="building-fire-nature">🔥 طبيعة الحريق:</label>
                <select id="building-fire-nature" name="fire_nature" class="form-control">
                    <option value="">اختر طبيعة الحريق</option>
                    <option value="residential_building">حريق بناية مخصصة للسكن</option>
                    <option value="classified_institution">حريق مؤسسة مصنفة</option>
                    <option value="public_place">حريق مكان مستقبل للجمهور</option>
                    <option value="vehicle_fire">حريق مركبة</option>
                    <option value="shop_market">حريق محل أو سوق</option>
                </select>
            </div>
            
            <!-- تفاصيل حسب النوع -->
            <div id="building-fire-nature-details" class="conditional-fields">
                
                <!-- تفاصيل البنايات السكنية -->
                <div id="residential-details" class="fire-nature-detail" style="display: none;">
                    <label for="residential-type">نوع البناية السكنية:</label>
                    <select id="residential-type" name="residential_type" class="form-control">
                        <option value="">اختر نوع البناية</option>
                        <option value="apartment">شقة</option>
                        <option value="individual_house">منزل فردي</option>
                        <option value="building">عمارة</option>
                        <option value="residential_complex">مجمع سكني</option>
                    </select>
                </div>
                
                <!-- تفاصيل المؤسسات المصنفة -->
                <div id="institution-details" class="fire-nature-detail" style="display: none;">
                    <label for="institution-type">نوع المؤسسة:</label>
                    <select id="institution-type" name="institution_type" class="form-control">
                        <option value="">اختر نوع المؤسسة</option>
                        <option value="factory">مصنع</option>
                        <option value="workshop">ورشة</option>
                        <option value="poultry_farm">مدجنة</option>
                        <option value="warehouse">مخزن</option>
                    </select>
                </div>
                
                <!-- تفاصيل الأماكن العامة -->
                <div id="public-place-details" class="fire-nature-detail" style="display: none;">
                    <label for="public-place-type">نوع المكان العام:</label>
                    <select id="public-place-type" name="public_place_type" class="form-control">
                        <option value="">اختر نوع المكان</option>
                        <option value="school">مدرسة</option>
                        <option value="hospital">مستشفى</option>
                        <option value="mosque">مسجد</option>
                        <option value="event_hall">قاعة حفلات</option>
                        <option value="shopping_center">مركز تجاري</option>
                    </select>
                </div>
                
                <!-- تفاصيل حريق المركبة -->
                <div id="vehicle-fire-details" class="fire-nature-detail" style="display: none;">
                    <label for="vehicle-fire-type">نوع المركبة:</label>
                    <select id="vehicle-fire-type" name="vehicle_type" class="form-control">
                        <option value="">اختر نوع المركبة</option>
                        <option value="car">سيارة</option>
                        <option value="truck">شاحنة</option>
                        <option value="bus">حافلة</option>
                        <option value="motorcycle">دراجة نارية</option>
                        <option value="other_vehicle">مركبة أخرى</option>
                    </select>
                </div>
            </div>
            
            <!-- موقع الحريق -->
            <div class="form-group">
                <label for="building-fire-location">📍 موقع الحريق:</label>
                <select id="building-fire-location" name="fire_location" class="form-control">
                    <option value="">اختر موقع الحريق</option>
                    <option value="inside_building">داخل البناية</option>
                    <option value="outside_building">خارج المبنى</option>
                    <option value="specific_floor">طابق معين</option>
                    <option value="specific_room">غرفة محددة</option>
                    <option value="threatened_area">مكان مهدد بالانتشار</option>
                </select>
            </div>
            
            <!-- تفاصيل الموقع المحدد -->
            <div id="specific-location-details" style="display: none;">
                <div class="form-group">
                    <label for="specific-floor">الطابق المحدد:</label>
                    <input type="text" id="specific-floor" name="specific_floor" class="form-control" placeholder="مثال: الطابق الثالث">
                </div>
                <div class="form-group">
                    <label for="specific-room">الغرفة المحددة:</label>
                    <input type="text" id="specific-room" name="specific_room" class="form-control" placeholder="مثال: غرفة النوم الرئيسية">
                </div>
            </div>
            
            <!-- انتشار الحريق -->
            <div class="fire-spread-section">
                <h5>🔥 انتشار الحريق</h5>
                
                <div class="form-group">
                    <label for="fire-points-count">عدد نقاط الاشتعال:</label>
                    <input type="number" id="fire-points-count" name="fire_points_count" class="form-control" min="0" value="0">
                </div>
                
                <div class="form-group">
                    <label for="wind-direction">جهة الرياح:</label>
                    <input type="text" id="wind-direction" name="wind_direction" class="form-control" placeholder="مثال: شمالية، جنوبية شرقية">
                </div>
                
                <div class="form-group">
                    <label for="wind-speed">سرعة الرياح (كم/سا):</label>
                    <input type="number" id="wind-speed" name="wind_speed" class="form-control" min="0" step="0.1" placeholder="0.0">
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="population-threat" name="population_threat" value="1">
                        تهديد السكان
                    </label>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="population-evacuated" name="population_evacuated" value="1">
                        تم إجلاء السكان
                    </label>
                </div>
                
                <div class="form-group">
                    <label for="assistance-provided">المساعدات المقدمة للسكان:</label>
                    <textarea id="assistance-provided" name="assistance_provided" class="form-control" rows="3" placeholder="وصف المساعدات المقدمة للسكان المتضررين"></textarea>
                </div>
            </div>
            
            <!-- طلب الدعم -->
            <div class="form-group">
                <label for="building-fire-support">🚨 طلب الدعم:</label>
                <select id="building-fire-support" name="support_request" class="form-control">
                    <option value="">اختر نوع الدعم</option>
                    <option value="under_control">شكراً، الوضع تحت السيطرة</option>
                    <option value="additional_vehicle">نعم وسيلة إضافية</option>
                    <option value="neighboring_unit">نعم وحدة مجاورة</option>
                    <option value="specialized_teams">نعم فرق متخصصة</option>
                </select>
            </div>
            
            <!-- ملاحظة عن الوضع والخسائر الميدانية -->
            <div class="form-group">
                <label for="field-situation-notes">📝 ملاحظة عن الوضع والخسائر الميدانية:</label>
                <textarea id="field-situation-notes" name="field_situation_notes" class="form-control" rows="4" placeholder="وصف مفصل للوضع الميداني والخسائر المبدئية"></textarea>
            </div>
        </div>
        
        <!-- مرحلة الإنهاء -->
        <div class="stage-section" id="building-fire-completion-stage">
            <h4 class="stage-title">
                <i class="fas fa-check-circle"></i>
                مرحلة إنهاء المهمة
            </h4>
            
            <!-- الإحصائيات النهائية -->
            <div class="form-group">
                <label for="intervening-agents-count">👨‍🚒 عدد الأعوان المتدخلين:</label>
                <input type="number" id="intervening-agents-count" name="intervening_agents_count" class="form-control" min="0" value="0">
            </div>
            
            <div class="form-group">
                <label for="affected-families-count">👨‍👩‍👧‍👦 عدد العائلات المتضررة:</label>
                <input type="number" id="affected-families-count" name="affected_families_count" class="form-control" min="0" value="0">
            </div>
            
            <!-- تفاصيل المسعفين -->
            <div class="form-group">
                <label>👨‍⚕️ تفاصيل المسعفين:</label>
                <div id="building-fire-injured-list" class="casualties-list">
                    <!-- سيتم إضافة المسعفين ديناميكياً -->
                </div>
                <button type="button" class="btn btn-secondary btn-sm" onclick="addBuildingFireCasualty('injured')">
                    <i class="fas fa-plus"></i> إضافة مسعف
                </button>
            </div>
            
            <!-- تفاصيل الوفيات -->
            <div class="form-group">
                <label>⚰️ تفاصيل الوفيات:</label>
                <div id="building-fire-fatalities-list" class="casualties-list">
                    <!-- سيتم إضافة الوفيات ديناميكياً -->
                </div>
                <button type="button" class="btn btn-secondary btn-sm" onclick="addBuildingFireCasualty('fatality')">
                    <i class="fas fa-plus"></i> إضافة وفاة
                </button>
            </div>
            
            <!-- الخسائر -->
            <div class="form-group">
                <label for="damages-description">💥 وصف الخسائر:</label>
                <textarea id="damages-description" name="damages_description" class="form-control" rows="4" placeholder="مثال: احتراق كلي لمحل تجاري يحتوي على مواد تجميل – احتراق جزئي لطابق علوي – تلف تجهيزات إلكترونية"></textarea>
            </div>
            
            <!-- الأملاك المنقذة -->
            <div class="form-group">
                <label for="saved-properties">🛡️ الأملاك المنقذة:</label>
                <textarea id="saved-properties" name="saved_properties" class="form-control" rows="4" placeholder="مثال: إنقاذ 4 أسطوانات غاز – منع امتداد الحريق إلى شقق الطابق الثالث – حماية محطة كهرباء مجاورة"></textarea>
            </div>
            
            <!-- ملاحظات ختامية -->
            <div class="form-group">
                <label for="building-fire-final-notes">📋 ملاحظات ختامية:</label>
                <textarea id="building-fire-final-notes" name="final_notes" class="form-control" rows="3" placeholder="ملاحظات إضافية حول التدخل"></textarea>
            </div>
        </div>
        
        <!-- أزرار الحفظ -->
        <div class="form-actions">
            <button type="button" class="btn btn-primary" onclick="saveBuildingFireDetails()">
                <i class="fas fa-save"></i>
                حفظ تفاصيل حريق البناية
            </button>
            <button type="button" class="btn btn-secondary" onclick="hideBuildingFireForm()">
                <i class="fas fa-times"></i>
                إلغاء
            </button>
        </div>
    </form>
</div>

<style>
.fire-spread-section {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.fire-spread-section h5 {
    color: #856404;
    margin-bottom: 15px;
}

.fire-nature-detail {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

#specific-location-details {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 4px;
    padding: 15px;
    margin-top: 10px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-weight: normal;
}

.checkbox-group input[type="checkbox"] {
    margin-left: 8px;
}
</style>

<!-- نموذج حوادث المرور المتخصص -->
<div id="traffic-accident-form" class="intervention-form-card" style="display: none;">
    <form id="trafficAccidentForm" class="intervention-form">
        <div class="form-section">
        <h3 class="section-title">
            <i class="fas fa-car-crash"></i>
            تفاصيل حادث المرور
        </h3>
        
        <!-- مرحلة التعرف -->
        <div class="stage-section" id="traffic-reconnaissance-stage">
            <h4 class="stage-title">
                <i class="fas fa-search"></i>
                مرحلة التعرف الميداني
            </h4>
            
            <!-- نوع الحادث -->
            <div class="form-group">
                <label for="traffic-accident-type">🚗 نوع الحادث:</label>
                <select id="traffic-accident-type" name="accident_type" class="form-control">
                    <option value="">اختر نوع الحادث</option>
                    <option value="vehicle_collision">ضحايا مصدومة بالمركبات</option>
                    <option value="vehicle_crash">ضحايا تصادم المركبات</option>
                    <option value="vehicle_rollover">ضحايا إنقلاب</option>
                    <option value="train_collision">ضحايا مصدومة بالقطار</option>
                    <option value="other_accidents">حوادث أخرى</option>
                </select>
            </div>
            
            <!-- تفاصيل المركبات المتورطة -->
            <div class="form-group">
                <label>🚙 المركبات المتورطة:</label>
                <div id="traffic-vehicles-list" class="vehicles-list">
                    <!-- سيتم إضافة المركبات ديناميكياً -->
                </div>
                <button type="button" class="btn btn-secondary btn-sm" onclick="addTrafficVehicle()">
                    <i class="fas fa-plus"></i> إضافة مركبة
                </button>
            </div>
            
            <!-- تفاصيل حسب نوع الحادث -->
            <div id="traffic-accident-details" class="conditional-fields">
                
                <!-- تفاصيل الصدم -->
                <div id="collision-details" class="accident-detail" style="display: none;">
                    <label>نوع المركبة الصادمة:</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="collision_vehicles" value="car"> سيارة</label>
                        <label><input type="checkbox" name="collision_vehicles" value="truck"> شاحنة</label>
                        <label><input type="checkbox" name="collision_vehicles" value="bus"> حافلة</label>
                        <label><input type="checkbox" name="collision_vehicles" value="motorcycle"> دراجة نارية</label>
                        <label><input type="checkbox" name="collision_vehicles" value="other"> أخرى</label>
                    </div>
                </div>
                
                <!-- تفاصيل التصادم -->
                <div id="crash-details" class="accident-detail" style="display: none;">
                    <label>نوع التصادم:</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="crash_types" value="car_car"> سيارة × سيارة</label>
                        <label><input type="checkbox" name="crash_types" value="car_truck"> سيارة × شاحنة</label>
                        <label><input type="checkbox" name="crash_types" value="truck_truck"> شاحنة × شاحنة</label>
                        <label><input type="checkbox" name="crash_types" value="other"> أخرى</label>
                    </div>
                </div>
                
                <!-- تفاصيل الانقلاب -->
                <div id="rollover-details" class="accident-detail" style="display: none;">
                    <label>نوع المركبة المنقلبة:</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="rollover_vehicles" value="car"> سيارة</label>
                        <label><input type="checkbox" name="rollover_vehicles" value="truck"> شاحنة</label>
                        <label><input type="checkbox" name="rollover_vehicles" value="bus"> حافلة</label>
                        <label><input type="checkbox" name="rollover_vehicles" value="other"> أخرى</label>
                    </div>
                </div>
                
                <!-- تفاصيل القطار -->
                <div id="train-details" class="accident-detail" style="display: none;">
                    <label>نوع الضحية:</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="train_victims" value="car"> سيارة</label>
                        <label><input type="checkbox" name="train_victims" value="person"> شخص</label>
                        <label><input type="checkbox" name="train_victims" value="truck"> شاحنة</label>
                        <label><input type="checkbox" name="train_victims" value="other"> أخرى</label>
                    </div>
                </div>
                
                <!-- حوادث أخرى -->
                <div id="other-accidents-details" class="accident-detail" style="display: none;">
                    <label>نوع الحادث:</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="other_accident_types" value="fall_from_vehicle"> سقوط من مركبة</label>
                        <label><input type="checkbox" name="other_accident_types" value="vehicle_fire"> حريق مركبة</label>
                        <label><input type="checkbox" name="other_accident_types" value="vehicle_explosion"> انفجار مركبة</label>
                    </div>
                </div>
            </div>
            
            <!-- ملاحظات عن الخسائر المادية -->
            <div class="form-group">
                <label for="traffic-material-damage">💰 ملاحظة حول الخسائر المادية:</label>
                <textarea id="traffic-material-damage" name="material_damage_notes" class="form-control" rows="3" placeholder="وصف الخسائر المادية والأضرار"></textarea>
            </div>
        </div>
        
        <!-- مرحلة الإنهاء -->
        <div class="stage-section" id="traffic-completion-stage">
            <h4 class="stage-title">
                <i class="fas fa-check-circle"></i>
                مرحلة إنهاء المهمة
            </h4>
            
            <!-- نوع الطريق -->
            <div class="form-group">
                <label for="traffic-road-type">🛣️ نوع الطريق:</label>
                <select id="traffic-road-type" name="road_type" class="form-control">
                    <option value="">اختر نوع الطريق</option>
                    <option value="highway">الطريق السيار</option>
                    <option value="national">الطريق الوطني</option>
                    <option value="wilaya">الطريق الولائي</option>
                    <option value="municipal">الطريق البلدي</option>
                    <option value="other">طرق أخرى</option>
                </select>
            </div>
            
            <!-- تفاصيل الضحايا -->
            <div class="form-group">
                <label>👥 تفاصيل الضحايا:</label>
                <div id="traffic-victims-list" class="casualties-list">
                    <!-- سيتم إضافة الضحايا ديناميكياً -->
                </div>
                <button type="button" class="btn btn-secondary btn-sm" onclick="addTrafficCasualty('victim')">
                    <i class="fas fa-plus"></i> إضافة ضحية
                </button>
            </div>
            
            <!-- تفاصيل الوفيات -->
            <div class="form-group">
                <label>⚰️ تفاصيل الوفيات:</label>
                <div id="traffic-fatalities-list" class="casualties-list">
                    <!-- سيتم إضافة الوفيات ديناميكياً -->
                </div>
                <button type="button" class="btn btn-secondary btn-sm" onclick="addTrafficCasualty('fatality')">
                    <i class="fas fa-plus"></i> إضافة وفاة
                </button>
            </div>
        </div>
        
        <!-- أزرار الحفظ -->
        <div class="form-actions">
            <button type="button" class="btn btn-primary" onclick="saveTrafficAccidentDetails()">
                <i class="fas fa-save"></i>
                حفظ تفاصيل حادث المرور
            </button>
            <button type="button" class="btn btn-secondary" onclick="hideTrafficForm()">
                <i class="fas fa-times"></i>
                إلغاء
            </button>
        </div>
    </div>
</div>

<style>
.vehicles-list {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
    min-height: 50px;
    background: #f8f9fa;
}

.vehicle-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.vehicle-item .vehicle-info {
    flex-grow: 1;
}

.vehicle-item .vehicle-actions {
    margin-left: 10px;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 10px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    font-weight: normal;
}

.checkbox-group input[type="checkbox"] {
    margin-left: 8px;
}

.accident-detail {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

.casualty-status-field {
    margin-top: 10px;
}

.casualty-status-field select {
    width: 100%;
    padding: 5px;
    border: 1px solid #ced4da;
    border-radius: 4px;
}
</style>

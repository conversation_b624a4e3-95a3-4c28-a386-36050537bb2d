<!-- نموذج حريق المحاصيل الزراعية المتخصص -->
<div id="agricultural-fire-form" class="intervention-form-card" style="display: none;">
    <form id="agriculturalFireForm" class="intervention-form">
        <div class="form-section">
        <h3 class="section-title">
            <i class="fas fa-seedling"></i>
            تفاصيل حريق المحاصيل الزراعية
        </h3>
        
        <!-- مرحلة التعرف -->
        <div class="stage-section" id="agricultural-fire-reconnaissance-stage">
            <h4 class="stage-title">
                <i class="fas fa-search"></i>
                مرحلة التعرف الميداني
            </h4>
            
            <!-- نوع الحريق -->
            <div class="form-group">
                <label for="agricultural-fire-type">🌾 نوع الحريق:</label>
                <select id="agricultural-fire-type" name="fire_type" class="form-control">
                    <option value="">اختر نوع الحريق</option>
                    <option value="standing_wheat">قمح واقف</option>
                    <option value="harvest">حصيدة</option>
                    <option value="barley">شعير</option>
                    <option value="straw_bales">حزم تبن</option>
                    <option value="forest_bushes">غابة / أحراش</option>
                    <option value="grain_bags">أكياس شعير / قمح</option>
                    <option value="fruit_trees">أشجار مثمرة</option>
                    <option value="beehives">خلايا نحل</option>
                </select>
            </div>
            
            <!-- انتشار الحريق -->
            <div class="fire-spread-section">
                <h5>🔥 انتشار الحريق</h5>
                
                <div class="form-group">
                    <label for="fire-sources-count">عدد البؤر (الموقد):</label>
                    <input type="number" id="fire-sources-count" name="fire_sources_count" class="form-control" min="0" value="0">
                </div>
                
                <div class="form-group">
                    <label for="agricultural-wind-direction">اتجاه الرياح:</label>
                    <input type="text" id="agricultural-wind-direction" name="wind_direction" class="form-control" placeholder="مثال: شمالية، جنوبية شرقية">
                </div>
                
                <div class="form-group">
                    <label for="agricultural-wind-speed">سرعة الرياح (كم/سا):</label>
                    <input type="number" id="agricultural-wind-speed" name="wind_speed" class="form-control" min="0" step="0.1" placeholder="0.0">
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="agricultural-population-threat" name="population_threat" value="1">
                        تهديد للسكان
                    </label>
                </div>
                
                <div class="form-group" id="evacuation-location-group" style="display: none;">
                    <label for="evacuation-location">مكان إجلاء السكان:</label>
                    <input type="text" id="evacuation-location" name="evacuation_location" class="form-control" placeholder="اسم المكان المخصص لإجلاء السكان">
                </div>
            </div>
            
            <!-- الأعوان والضحايا -->
            <div class="personnel-section">
                <h5>👥 الأعوان والضحايا</h5>
                
                <div class="form-group">
                    <label for="agricultural-intervening-agents">عدد الأعوان المتدخلين:</label>
                    <input type="number" id="agricultural-intervening-agents" name="intervening_agents_count" class="form-control" min="0" value="0">
                </div>
                
                <div class="form-group">
                    <label for="agricultural-affected-families">عدد العائلات المتأثرة:</label>
                    <input type="number" id="agricultural-affected-families" name="affected_families_count" class="form-control" min="0" value="0">
                </div>
            </div>
            
            <!-- الجهات الحاضرة -->
            <div class="form-group">
                <label>👮‍♂️ الجهات الحاضرة:</label>
                <div class="checkbox-group">
                    <label><input type="checkbox" name="present_authorities" value="gendarmerie"> درك</label>
                    <label><input type="checkbox" name="present_authorities" value="police"> شرطة</label>
                    <label><input type="checkbox" name="present_authorities" value="forest_guards"> حراس غابات</label>
                    <label><input type="checkbox" name="present_authorities" value="local_authorities"> سلطات محلية</label>
                    <label><input type="checkbox" name="present_authorities" value="other"> أخرى</label>
                </div>
            </div>
            
            <!-- طلب الدعم -->
            <div class="form-group">
                <label for="agricultural-support-request">🚨 طلب دعم:</label>
                <select id="agricultural-support-request" name="support_request" class="form-control">
                    <option value="">اختر نوع الدعم</option>
                    <option value="under_control">شكراً، الوضع تحت السيطرة</option>
                    <option value="additional_vehicle">نعم وسيلة إضافية</option>
                    <option value="neighboring_unit">نعم وحدة مجاورة</option>
                    <option value="specialized_teams">نعم فرق متخصصة</option>
                </select>
            </div>
            
            <!-- نوع الفريق المتخصص -->
            <div id="agricultural-specialized-team-section" class="form-group" style="display: none;">
                <label for="agricultural-specialized-team-type">نوع الفريق المتخصص:</label>
                <select id="agricultural-specialized-team-type" name="specialized_team_type" class="form-control">
                    <option value="">اختر نوع الفريق</option>
                    <option value="divers">فرقة الغطس</option>
                    <option value="rough_terrain">فرقة الأماكن الوعرة</option>
                    <option value="cynotechnical">فرقة السينوتقنية</option>
                    <option value="other">أخرى</option>
                </select>
            </div>
        </div>
        
        <!-- مرحلة الإنهاء -->
        <div class="stage-section" id="agricultural-fire-completion-stage">
            <h4 class="stage-title">
                <i class="fas fa-check-circle"></i>
                مرحلة إنهاء المهمة
            </h4>
            
            <!-- الخسائر حسب المساحة -->
            <div class="damages-by-area-section">
                <h5>📏 الخسائر حسب المساحة (هكتار)</h5>
                
                <div class="form-row">
                    <div class="form-group col-md-6">
                        <label for="standing-wheat-area">قمح واقف:</label>
                        <input type="number" id="standing-wheat-area" name="standing_wheat_area" class="form-control" min="0" step="0.01" value="0">
                    </div>
                    <div class="form-group col-md-6">
                        <label for="harvest-area">حصيدة:</label>
                        <input type="number" id="harvest-area" name="harvest_area" class="form-control" min="0" step="0.01" value="0">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group col-md-6">
                        <label for="forest-area">غابة/أحراش:</label>
                        <input type="number" id="forest-area" name="forest_area" class="form-control" min="0" step="0.01" value="0">
                    </div>
                    <div class="form-group col-md-6">
                        <label for="barley-area">شعير:</label>
                        <input type="number" id="barley-area" name="barley_area" class="form-control" min="0" step="0.01" value="0">
                    </div>
                </div>
            </div>
            
            <!-- الخسائر حسب العدد -->
            <div class="damages-by-count-section">
                <h5>🔢 الخسائر حسب العدد</h5>
                
                <div class="form-row">
                    <div class="form-group col-md-6">
                        <label for="straw-bales-count">حزم تبن:</label>
                        <input type="number" id="straw-bales-count" name="straw_bales_count" class="form-control" min="0" value="0">
                    </div>
                    <div class="form-group col-md-6">
                        <label for="grain-bags-count">أكياس قمح/شعير:</label>
                        <input type="number" id="grain-bags-count" name="grain_bags_count" class="form-control" min="0" value="0">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group col-md-6">
                        <label for="fruit-trees-count">أشجار مثمرة:</label>
                        <input type="number" id="fruit-trees-count" name="fruit_trees_count" class="form-control" min="0" value="0">
                    </div>
                    <div class="form-group col-md-6">
                        <label for="beehives-count">خلايا نحل:</label>
                        <input type="number" id="beehives-count" name="beehives_count" class="form-control" min="0" value="0">
                    </div>
                </div>
            </div>
            
            <!-- الأملاك المنقذة -->
            <div class="saved-properties-section">
                <h5>🛡️ الأملاك المنقذة</h5>
                
                <div class="form-group">
                    <label for="saved-area">مساحة منقذة (هكتار):</label>
                    <input type="number" id="saved-area" name="saved_area" class="form-control" min="0" step="0.01" value="0">
                </div>
                
                <div class="form-group">
                    <label for="saved-straw-bales">عدد حزم التبن المنقذة:</label>
                    <input type="number" id="saved-straw-bales" name="saved_straw_bales" class="form-control" min="0" value="0">
                </div>
                
                <div class="form-group">
                    <label for="saved-equipment">ممتلكات أو آلات تم إنقاذها:</label>
                    <textarea id="saved-equipment" name="saved_equipment" class="form-control" rows="3" placeholder="وصف الممتلكات والآلات التي تم إنقاذها"></textarea>
                </div>
            </div>
            
            <!-- تفاصيل الضحايا -->
            <div class="form-group">
                <label>👥 تفاصيل الضحايا:</label>
                <div id="agricultural-victims-list" class="casualties-list">
                    <!-- سيتم إضافة الضحايا ديناميكياً -->
                </div>
                <button type="button" class="btn btn-secondary btn-sm" onclick="addAgriculturalCasualty('victim')">
                    <i class="fas fa-plus"></i> إضافة ضحية
                </button>
            </div>
            
            <!-- تفاصيل الوفيات -->
            <div class="form-group">
                <label>⚰️ تفاصيل الوفيات:</label>
                <div id="agricultural-fatalities-list" class="casualties-list">
                    <!-- سيتم إضافة الوفيات ديناميكياً -->
                </div>
                <button type="button" class="btn btn-secondary btn-sm" onclick="addAgriculturalCasualty('fatality')">
                    <i class="fas fa-plus"></i> إضافة وفاة
                </button>
            </div>
            
            <!-- ملاحظات ختامية -->
            <div class="form-group">
                <label for="agricultural-final-notes">📋 ملاحظات ختامية:</label>
                <textarea id="agricultural-final-notes" name="final_notes" class="form-control" rows="3" placeholder="ملاحظات إضافية حول التدخل"></textarea>
            </div>
        </div>
        
        <!-- أزرار الحفظ -->
        <div class="form-actions">
            <button type="button" class="btn btn-primary" onclick="saveAgriculturalFireDetails()">
                <i class="fas fa-save"></i>
                حفظ تفاصيل حريق المحاصيل
            </button>
            <button type="button" class="btn btn-secondary" onclick="hideAgriculturalFireForm()">
                <i class="fas fa-times"></i>
                إلغاء
            </button>
        </div>
    </form>
</div>

<style>
.personnel-section, .damages-by-area-section, .damages-by-count-section, .saved-properties-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.personnel-section h5, .damages-by-area-section h5, .damages-by-count-section h5, .saved-properties-section h5 {
    color: #495057;
    margin-bottom: 15px;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 5px;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-left: -5px;
    margin-right: -5px;
}

.form-row .form-group {
    padding-left: 5px;
    padding-right: 5px;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
}

@media (max-width: 768px) {
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 10px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    font-weight: normal;
}

.checkbox-group input[type="checkbox"] {
    margin-left: 8px;
}
</style>

# 📝 واجهة التدخلات اليومية – مركز التنسيق للوحدة

---

## 🎯 الهدف

تمكين مركز التنسيق للوحدة من:

- تسجيل التدخلات اليومية (حوادث مرور، إجلاء صحي،  بسيطة...)
- متابعة التدخل من لحظة البلاغ الأولي حتى انتهاء المهمة
- طلب الدعم من وحدات مجاورة أو مركز التنسيق الولائي
- تصعيد الحادث إلى "كارثة كبرى" عند الضرورة

---

## 🔲 مكونات الصفحة

تتكون الواجهة من:

### 🔘 ثلاثة أزرار رئيسية:

1️⃣ **📢 بلاغ أولي**  
2️⃣ **🧭 عملية التعرف**  
3️⃣ **✅ إنهاء المهمة**

---

## 1️⃣ 📢 بلاغ أولي

يُستخدم عند تلقي اتصال من مواطن، شرطة، أو درك.

### 📝 البيانات المدخلة:

- 🕒 ساعة ودقيقة الخروج
- 📍 مكان الحادث
-  نوع التدخل (حرائق البنايات والمؤسسات  حريق محاصيل زراعية، حادث مرور، إجلاء صحي...)
- 🚒 الوسيلة المرسلة (شاحنة إطفاء، سيارة إسعاف...)
- 👤 الجهة المتصلة (مواطن، شرطة، درك)
- ☎️ نوع الاتصال (هاتف، راديو)
- 📞 رقم الهاتف
- 📝 ملاحظات إضافية

🟡 **الحالة:** `قيد التعرف`

---

## 2️⃣ 🧭 عملية التعرف

بعد وصول الفريق إلى مكان الحادث، يتصل العون الميداني بالمركز ويدخل رئيس المركز المعلومات التالية:

- 👥 عدد الحاضرين
- 🚑 عدد الضحايا أو المصابين
- ❌ عدد الذين رفضوا النقل
- 🏚️ ملاحظة عن الخسائر المادية إن ظهرت

### 📤 طلب الدعم:

- ✅ في حال توفر وسيلة: تُختار من قائمة الوسائل.
- 🚨 إن لم تتوفر وسيلة: يتم اختيار وحدة مجاورة → يصدر إنذار صوتي تلقائي لها.
- تُسجل الوسيلة الداعمة في نفس جدول التدخل كسطر مستقل.

🟡 **الحالة:** `عملية تدخل`

---

## 3️⃣ ✅ إنهاء المهمة

يتم إدخال المعلومات النهائية بعد انتهاء التدخل ميدانيًا.  
في هذه المرحلة فقط، يتم تقييم الخسائر بدقة وتوثيق النتيجة.

### 📝 البيانات المدخلة:

#### 📌 عامة لجميع التدخلات:

- 👥 أسماء الضحايا (إن وُجدوا)
- 🎂 أعمارهم
- ❤️‍🩹 طبيعة الإصابات (خفيفة، متوسطة، خطيرة)
- ⚰️ عدد الوفيات
- 📝 ملاحظات ختامية

#### 🔥 إذا كان التدخل "حريق" (حصيدة، غابة، منزل...):

##### 🔻 الخسائر المسجلة:

- 🌾 المساحة المحترقة (هكتارات)
- 📦 عدد حزم التبن المحترقة (إن وُجدت)
- 🏚️ خسائر مادية أخرى (آلات، مركبات، مباني...)

##### 🟢 الأملاك المنقذة:

- 🌿 المساحة المنقذة (هكتارات)
- 📦 عدد الحزم المنقذة
- 🚜 المعدات أو الآلات التي تم إنقاذها

🟢 **الحالة النهائية:** `منتهية`

---

## 🗃️ جدول التدخلات اليومية

يعرض جميع التدخلات لحظة بلحظة، ويتضمن:

| رقم | وقت التدخل | نوع التدخل | الوحدة | الوسيلة | الحالة | إجراء |
|-----|--------------|--------------|---------|----------|----------|--------|
| 23  | 12:47        | حريق         | بئر بوحوش | FPT-05  | قيد التعرف | 📝 |
| 24  | 14:20        | إجلاء صحي     | المشروحة  | AMB-02 | عملية تدخل | 🔁 |
| 25  | 17:10        | حادث مرور     | سوق أهراس | FPT-01 | منتهية     | 👁️ |

---

## 📤 طلب الدعم والتصعيد

### 🟡 دعم من وحدة مجاورة:

- يتم اختيار الوسيلة يدويًا
- يُسجل التدخل في جدول الوحدة الداعمة تلقائيًا
- تُربط الوسيلة بالسطر الأصلي للتدخل

### 🔴 طلب دعم لحادث ضمن الوحدة نفسها:

- لا يمكن للوحدة اختيار الدعم بنفسها
- 🔁 يُرسل الطلب تلقائيًا إلى **مركز التنسيق الولائي**
- الولائي يختار الوحدة المناسبة، ويصدر لها إنذار صوتي
- تُسجل الوسيلة المتدخلة في جدول الوحدة الداعمة

---

## 🔺 تصعيد إلى كارثة كبرى

عندما يُلاحظ مركز التنسيق الولائي أن التدخل خرج عن السيطرة:

- يضغط على زر: **⚠️ تصعيد إلى الكوارث الكبرى**
- يُحوّل التدخل تلقائيًا إلى صفحة الكوارث الكبرى
- يتم متابعة التطورات عبر واجهة الكوارث

🟥 **الحالة تتحول إلى:** `كارثة كبرى`

---

## 🔗 ملاحظات تقنية

- لكل تدخل UUID فريد
- كل مرحلة (بلاغ – تعرف – إنهاء) تُحدث نفس السطر
- عند طلب دعم، تُسجل الوسيلة في جدول الوحدة المتدخلة
- عند التصعيد، تنتقل البيانات إلى صفحة "الكوارث الكبرى"
- يمكن لاحقًا توليد تقارير PDF وإرفاق خرائط التدخل

